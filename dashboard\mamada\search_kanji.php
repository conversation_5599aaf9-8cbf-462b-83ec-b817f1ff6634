<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Log inicial para verificar que el script se ejecuta
$msg = "🚀 Script search_kanji.php iniciado - " . date('Y-m-d H:i:s');
file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
error_log($msg);

// Configuración
$host = '127.0.0.1';
$dbname = 'u636704306_Dictionaries';
$user = 'u636704306_Kevs';
$pass = '5@8W>|NRe';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
];

// Detecta si el texto es japonés (kanji, hiragana o katakana)
function esJapones($text) {
    return preg_match('/[\x{3040}-\x{30FF}\x{4E00}-\x{9FFF}]/u', $text);
}

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (PDOException $e) {
    $msg = '❌ Error de conexión: ' . $e->getMessage();
    file_put_contents('log_kanji.txt', $msg);
    error_log($msg);
    http_response_code(500);
    echo json_encode(['error' => $msg]);
    exit;
}

// Obtener y limpiar parámetro
$q = isset($_GET['q']) ? trim($_GET['q']) : '';
$type = isset($_GET['type']) ? trim($_GET['type']) : 'kanji';

$msg = "📥 Parámetros recibidos - q: '$q', type: '$type'";
file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
error_log($msg);

if ($q === '') {
    $msg = "❌ Consulta vacía, devolviendo array vacío";
    file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
    echo json_encode([]);
    exit;
}

$wildcard = "%$q%";

try {
    $msg = "� Usando tabla: kanji_dict (única tabla disponible)";
    file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
    error_log($msg);

    // Construir SQL dependiendo del tipo de búsqueda
    if ($type === 'meaning') {
        // Búsqueda por significado
        $sql = "
            SELECT
                li_tx as literal,
                mn_ln_en_vl as meanings,
                rM_g_rd_on_vl as on_readings,
                rM_g_rd_kun_vl as kun_readings,
                ms_sC as stroke_count,
                ms_fr as frequency,
                ms_jlpt as jlpt
            FROM kanji_dict
            WHERE
                LOWER(mn_ln_en_vl) LIKE ? OR
                LOWER(rM_g_rd_on_vl) LIKE ? OR
                LOWER(rM_g_rd_kun_vl) LIKE ?
        ";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $wildcard,
            $wildcard,
            $wildcard
        ]);
    } else {
        // Búsqueda por kanji
        if (esJapones($q)) {
            // Búsqueda exacta si es un solo carácter
            if (mb_strlen($q, 'UTF-8') === 1) {
                $sql = "
                    SELECT
                        li_tx as literal,
                        mn_ln_en_vl as meanings,
                        rM_g_rd_on_vl as on_readings,
                        rM_g_rd_kun_vl as kun_readings,
                        ms_sC as stroke_count,
                        ms_fr as frequency,
                        ms_jlpt as jlpt
                    FROM kanji_dict
                    WHERE li_tx = ?
                ";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$q]);
            } else {
                // Búsqueda de múltiples caracteres
                $sql = "
                    SELECT
                        li_tx as literal,
                        mn_ln_en_vl as meanings,
                        rM_g_rd_on_vl as on_readings,
                        rM_g_rd_kun_vl as kun_readings,
                        ms_sC as stroke_count,
                        ms_fr as frequency,
                        ms_jlpt as jlpt
                    FROM kanji_dict
                    WHERE li_tx IN (
                ";
                $params = [];

                for ($i = 0; $i < mb_strlen($q, 'UTF-8'); $i++) {
                    $char = mb_substr($q, $i, 1, 'UTF-8');
                    if (esJapones($char)) {
                        $sql .= "?,";
                        $params[] = $char;
                    }
                }

                $sql = rtrim($sql, ',') . ")";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
            }
        } else {
            // Si no es japonés y no es búsqueda por significado, devolver vacío
            $msg = "❌ Query no es japonés y no es búsqueda por significado";
            file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
            echo json_encode([]);
            exit;
        }
    }

    $results = $stmt->fetchAll();

    $msg = "✅ Kanji encontrados: " . count($results) . " para la consulta: " . $q;
    file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
    error_log($msg);

    echo json_encode($results);
} catch (PDOException $e) {
    $msg = '❌ Error en consulta SQL de kanji: ' . $e->getMessage();
    file_put_contents('log_kanji.txt', $msg . PHP_EOL, FILE_APPEND);
    error_log($msg);
    http_response_code(500);
    echo json_encode(['error' => $msg]);
    exit;
}



