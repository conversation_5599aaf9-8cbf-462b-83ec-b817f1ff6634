<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Configuración
$host = '127.0.0.1';
$dbname = 'u636704306_Dictionaries';
$user = 'u636704306_Kevs';
$pass = '5@8W>|NRe';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
];

// Función para limpiar gloss
function limpiarGloss($gloss) {
    return preg_replace('/\s*{\s*"pS":.*$/s', '', $gloss);
}

// Detecta si el texto es japonés (kanji, hiragana o katakana)
function esJapones($text) {
    return preg_match('/[\x{3040}-\x{30FF}\x{4E00}-\x{9FFF}]/u', $text);
}

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (PDOException $e) {
    $msg = '❌ Error de conexión: ' . $e->getMessage();
    file_put_contents('log.txt', $msg);
    error_log($msg);
    http_response_code(500);
    echo json_encode(['error' => $msg]);
    exit;
}

// Obtener y limpiar parámetro
$q = isset($_GET['q']) ? mb_strtolower(trim($_GET['q']), 'UTF-8') : '';
if ($q === '') {
    echo json_encode([]);
    exit;
}

$wildcard = "%$q%";

try {
    // Construir SQL dependiendo si es japonés o no
    if (esJapones($q)) {
        $sqlIds = "
            SELECT DISTINCT ID FROM registros_full
            WHERE 
                LOWER(KJ_tx) = ? OR 
                LOWER(KN_tx) = ? OR 
                LOWER(KJ_tx) LIKE ? OR 
                LOWER(KN_tx) LIKE ?
        ";
        $stmtIds = $pdo->prepare($sqlIds);
        $stmtIds->execute([$q, $q, $wildcard, $wildcard]);
    } else {
        $sqlIds = "
            SELECT DISTINCT ID FROM registros_full
            WHERE 
                LOWER(SE_GL_1_tx) LIKE ? OR 
                LOWER(SE_GL_2_tx) LIKE ?
        ";
        $stmtIds = $pdo->prepare($sqlIds);
        $stmtIds->execute([$wildcard, $wildcard]);
    }

    $ids = $stmtIds->fetchAll(PDO::FETCH_COLUMN);

    if (count($ids) === 0) {
        echo json_encode([]);
        exit;
    }

    $placeholders = implode(',', array_fill(0, count($ids), '?'));
    $sql = "SELECT * FROM registros_full WHERE ID IN ($placeholders)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($ids);
    $results = $stmt->fetchAll();

    $msg = "✅ Resultados encontrados: " . count($results);
    file_put_contents('log.txt', $msg);
    error_log($msg);

    foreach ($results as &$entry) {
        $entry['KJ_co'] = $entry['KJ_co'] === '1';
        $entry['KN_co'] = $entry['KN_co'] === '1';

        $entry['KJ_tg'] = isset($entry['KJ_tg']) && is_string($entry['KJ_tg']) ? explode(',', $entry['KJ_tg']) : [];
        $entry['KN_tg'] = isset($entry['KN_tg']) && is_string($entry['KN_tg']) ? explode(',', $entry['KN_tg']) : [];

        $entry['SE_pS'] = isset($entry['SE_pS']) && is_string($entry['SE_pS']) ? explode(',', $entry['SE_pS']) : [];
        $entry['SE_ms'] = isset($entry['SE_ms']) && is_string($entry['SE_ms']) ? explode(',', $entry['SE_ms']) : [];

        $entry['SE_GL_1_tx'] = limpiarGloss($entry['SE_GL_1_tx'] ?? '');
        $entry['SE_GL_2_tx'] = limpiarGloss($entry['SE_GL_2_tx'] ?? '');

        $entry['jlptLevel'] = $entry['jlptLevel'] ?? null;
        $entry['id'] = $entry['ID'] ?? $entry['id'] ?? null;
    }

    echo json_encode($results);
} catch (PDOException $e) {
    $msg = '❌ Error en consulta SQL: ' . $e->getMessage();
    file_put_contents('log.txt', $msg);
    error_log($msg);
    http_response_code(500);
    echo json_encode(['error' => $msg]);
    exit;
}




